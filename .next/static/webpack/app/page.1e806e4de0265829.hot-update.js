"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DatabaseSetup.tsx":
/*!******************************************!*\
  !*** ./src/components/DatabaseSetup.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseSetup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DatabaseSetup(param) {\n    let { onConnectionSuccess } = param;\n    _s();\n    const [connectionData, setConnectionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        server: 'localhost',\n        database: '',\n        username: 'myuser',\n        password: '',\n        port: '1433',\n        trustServerCertificate: true\n    });\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isIndexing, setIsIndexing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setConnectionData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setConnectionStatus('idle');\n        setErrorMessage('');\n    };\n    const testConnection = async ()=>{\n        setIsConnecting(true);\n        setConnectionStatus('testing');\n        setErrorMessage('');\n        try {\n            const response = await fetch('/api/database/test-connection', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(connectionData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setConnectionStatus('success');\n            } else {\n                setConnectionStatus('error');\n                setErrorMessage(result.error || 'فشل في الاتصال بقاعدة البيانات');\n            }\n        } catch (error) {\n            setConnectionStatus('error');\n            setErrorMessage('خطأ في الشبكة أو الخادم');\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    const startIndexing = async ()=>{\n        setIsIndexing(true);\n        try {\n            const response = await fetch('/api/database/index-schema', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(connectionData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                onConnectionSuccess({\n                    ...connectionData,\n                    schema: result.schema,\n                    indexed: true\n                });\n            } else {\n                setErrorMessage(result.error || 'فشل في فهرسة قاعدة البيانات');\n            }\n        } catch (error) {\n            setErrorMessage('خطأ في فهرسة قاعدة البيانات');\n        } finally{\n            setIsIndexing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-blue-600 dark:text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"وكيل الذكاء الاصطناعي لـ SQL\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"اتصل بقاعدة بيانات SQL Server للبدء\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    onSubmit: (e)=>e.preventDefault(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"اسم الخادم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: connectionData.server,\n                                    onChange: (e)=>handleInputChange('server', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"localhost أو عنوان IP\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"اسم قاعدة البيانات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: connectionData.database,\n                                    onChange: (e)=>handleInputChange('database', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"اسم قاعدة البيانات\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"اسم المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: connectionData.username,\n                                            onChange: (e)=>handleInputChange('username', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                            placeholder: \"sa\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"المنفذ\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: connectionData.port,\n                                            onChange: (e)=>handleInputChange('port', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                            placeholder: \"1433\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"كلمة المرور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: connectionData.password,\n                                    onChange: (e)=>handleInputChange('password', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"كلمة المرور\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"trustCert\",\n                                    checked: connectionData.trustServerCertificate,\n                                    onChange: (e)=>handleInputChange('trustServerCertificate', e.target.checked),\n                                    className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"trustCert\",\n                                    className: \"mr-2 text-sm text-gray-700 dark:text-gray-300\",\n                                    children: \"الثقة في شهادة الخادم\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700 dark:text-red-400 text-sm\",\n                                    children: errorMessage\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        connectionStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-700 dark:text-green-400 text-sm\",\n                                    children: \"تم الاتصال بنجاح!\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: testConnection,\n                                    disabled: isConnecting || !connectionData.server || !connectionData.database,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center\",\n                                    children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"جاري الاختبار...\"\n                                        ]\n                                    }, void 0, true) : 'اختبار الاتصال'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                connectionStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: startIndexing,\n                                    disabled: isIndexing,\n                                    className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center\",\n                                    children: isIndexing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"جاري الفهرسة...\"\n                                        ]\n                                    }, void 0, true) : 'البدء والفهرسة'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseSetup, \"CjHnZgadPx1vz+70uQW2fnDSCNA=\");\n_c = DatabaseSetup;\nvar _c;\n$RefreshReg$(_c, \"DatabaseSetup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DatabaseSetup.tsx\n"));

/***/ })

});