"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DatabaseSetup.tsx":
/*!******************************************!*\
  !*** ./src/components/DatabaseSetup.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseSetup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Database,Loader2,Lock,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DatabaseSetup(param) {\n    let { onConnectionSuccess } = param;\n    _s();\n    const [connectionData, setConnectionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        server: 'localhost',\n        database: 'SalesTempDB',\n        username: 'myuser',\n        password: 'Aa227520',\n        port: '1433',\n        trustServerCertificate: true\n    });\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isIndexing, setIsIndexing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setConnectionData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setConnectionStatus('idle');\n        setErrorMessage('');\n    };\n    const testConnection = async ()=>{\n        setIsConnecting(true);\n        setConnectionStatus('testing');\n        setErrorMessage('');\n        try {\n            const response = await fetch('/api/database/test-connection', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(connectionData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setConnectionStatus('success');\n            } else {\n                setConnectionStatus('error');\n                setErrorMessage(result.error || 'فشل في الاتصال بقاعدة البيانات');\n            }\n        } catch (error) {\n            setConnectionStatus('error');\n            setErrorMessage('خطأ في الشبكة أو الخادم');\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    const startIndexing = async ()=>{\n        setIsIndexing(true);\n        try {\n            const response = await fetch('/api/database/index-schema', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(connectionData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                onConnectionSuccess({\n                    ...connectionData,\n                    schema: result.schema,\n                    indexed: true\n                });\n            } else {\n                setErrorMessage(result.error || 'فشل في فهرسة قاعدة البيانات');\n            }\n        } catch (error) {\n            setErrorMessage('خطأ في فهرسة قاعدة البيانات');\n        } finally{\n            setIsIndexing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-blue-600 dark:text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"وكيل الذكاء الاصطناعي لـ SQL\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"اتصل بقاعدة بيانات SQL Server للبدء\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    onSubmit: (e)=>e.preventDefault(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"اسم الخادم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: connectionData.server,\n                                    onChange: (e)=>handleInputChange('server', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"localhost أو عنوان IP\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"اسم قاعدة البيانات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: connectionData.database,\n                                    onChange: (e)=>handleInputChange('database', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"اسم قاعدة البيانات\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"اسم المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: connectionData.username,\n                                            onChange: (e)=>handleInputChange('username', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                            placeholder: \"sa\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"المنفذ\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: connectionData.port,\n                                            onChange: (e)=>handleInputChange('port', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                            placeholder: \"1433\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 inline mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"كلمة المرور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: connectionData.password,\n                                    onChange: (e)=>handleInputChange('password', e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\",\n                                    placeholder: \"كلمة المرور\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"trustCert\",\n                                    checked: connectionData.trustServerCertificate,\n                                    onChange: (e)=>handleInputChange('trustServerCertificate', e.target.checked),\n                                    className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"trustCert\",\n                                    className: \"mr-2 text-sm text-gray-700 dark:text-gray-300\",\n                                    children: \"الثقة في شهادة الخادم\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700 dark:text-red-400 text-sm\",\n                                    children: errorMessage\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        connectionStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-700 dark:text-green-400 text-sm\",\n                                    children: \"تم الاتصال بنجاح!\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: testConnection,\n                                    disabled: isConnecting || !connectionData.server || !connectionData.database,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center\",\n                                    children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"جاري الاختبار...\"\n                                        ]\n                                    }, void 0, true) : 'اختبار الاتصال'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                connectionStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: startIndexing,\n                                    disabled: isIndexing,\n                                    className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center\",\n                                    children: isIndexing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Database_Loader2_Lock_Server_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"جاري الفهرسة...\"\n                                        ]\n                                    }, void 0, true) : 'البدء والفهرسة'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/py/src/components/DatabaseSetup.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseSetup, \"chLtIS5TCeXCXOxC2d0F51oHH2o=\");\n_c = DatabaseSetup;\nvar _c;\n$RefreshReg$(_c, \"DatabaseSetup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DatabaseSetup.tsx\n"));

/***/ })

});